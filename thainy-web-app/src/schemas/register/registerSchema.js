import * as z from 'zod';

// Thai phone number validation regex (starts with 0 and has 10 digits total)
const thaiPhoneRegex = /^0[0-9]{9}$/;

export const registerSchema = z.object({
    hospital: z.string().min(1, 'Please select a hospital'),
    role: z.string().refine(val => ['Leader', 'Doctor', 'Collector'].includes(val), {
        message: "Please select a role",
    }),
    firstName: z.string().min(2, 'First name must be at least 2 characters'),
    lastName: z.string().min(2, 'Last name must be at least 2 characters'),
    dateOfBirth: z.string().min(1, 'Please select date of birth'),
    telephone: z.string()
        .regex(thaiPhoneRegex, 'Please enter a valid Thai phone number (10 digits starting with 0)'),
    email: z.string().email('Please enter a valid email address'),
    username: z.string().min(3, 'Username must be at least 3 characters'),
    password: z.string()
        .min(6, 'Password must be at least 6 characters')
        .max(12, 'Password must not exceed 12 characters')
        .regex(/[A-Z]/, 'Password must contain at least 1 uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least 1 lowercase letter')
        .regex(/[0-9]/, 'Password must contain at least 1 number'),
    confirmPassword: z.string(),
    // Certificate attachment is optional
    certificate: z.any().optional(),
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});


export const registerDefaultValues = {
    hospital: "",
    role: "",
    firstName: "",
    lastName: "",
    dateOfBirth: "",
    telephone: "",
    email: "",
    username: "",
    password: "",
    confirmPassword: "",
};