import { useSelector, useDispatch } from 'react-redux';
import { loginUser, logoutUser, clearError } from '../features/auth/authSlice';

/**
 * Custom hook for authentication
 * Provides easy access to auth state and actions
 */
export const useAuth = () => {
  const dispatch = useDispatch();
  const authState = useSelector((state) => state.auth);

  const login = async (credentials) => {
    try {
      const result = await dispatch(loginUser(credentials)).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  };

  const logout = async () => {
    try {
      await dispatch(logoutUser()).unwrap();
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  };

  const clearAuthError = () => {
    dispatch(clearError());
  };

  return {
    // Auth state
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    token: authState.token,
    loading: authState.loading,
    error: authState.error,
    
    // Auth actions
    login,
    logout,
    clearAuthError,
  };
};
