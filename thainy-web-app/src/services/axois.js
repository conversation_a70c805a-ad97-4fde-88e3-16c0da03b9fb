import axios from "axios";
const baseUrl =
  import.meta.env.VITE_MODE === "prod"
    ? import.meta.env.VITE_SERVER_BASE_URL
    : import.meta.env.VITE_SERVER_BASE_URL_PROD;
const token = localStorage.getItem("token") || sessionStorage.getItem("token");
// const userId = localStorage.getItem("userId");

const axiosInstance = axios.create({
  baseURL: baseUrl, // Replace with your API base URL
  headers: {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "true",
  },
});

axiosInstance.interceptors.request.use((config) => {
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    const status = error.response?.status;

    if (status === 401 || status === 403) {
      alert("You don't have permission to perform this action.");

      localStorage.clear();
      sessionStorage.clear();

      window.location ="index#/invalid";
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
