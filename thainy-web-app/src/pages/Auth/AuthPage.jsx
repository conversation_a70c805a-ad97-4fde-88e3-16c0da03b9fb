import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LoginForm from './Login';
import RegisterForm from './RegisterForm';

const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <Header isLogin={isLogin} setIsLogin={setIsLogin} />

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center p-4 md:p-6" style={{ backgroundColor: '#e8e8e8' }}>
        <div className="w-full max-w-2xl  -mt-15 md:-mt-20">
          <Card className="shadow-lg bg-white">
            <CardContent>
              {isLogin ? (
                <LoginForm />
              ) : (
                <RegisterForm onCancel={() => setIsLogin(true)} />
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <Footer/>
    </div>
  );
};

export default AuthPage;