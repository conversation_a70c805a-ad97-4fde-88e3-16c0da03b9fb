import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { setAuthToken, setUserData } from '../../utils/auth';
import axiosInstance from '../../services/axois';

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export default function LoginForm() {
    const [isLoading, setIsLoading] = useState(false);
    const navigate = useNavigate();

    const loginForm = useForm({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
        },
    });

    const onLoginSubmit = async (data) => {
        setIsLoading(true);
        try {
            console.log('Login data:', data);

            // For demo purposes, simulate a successful login
            // Replace this with actual API call using axiosInstance
            // const response = await axiosInstance.post('/auth/login', data);

            // Simulate API response
            const mockToken = 'demo-auth-token-' + Date.now();
            const mockUserData = {
                id: 1,
                email: data.email,
                name: 'Demo User',
                role: 'user'
            };

            // Store authentication data
            setAuthToken(mockToken);
            setUserData(mockUserData);

            // Redirect to dashboard
            navigate('/dashboard');

        } catch (error) {
            console.error('Login error:', error);
            // Handle login error (show toast, error message, etc.)
            alert('Login failed. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Form {...loginForm}>
            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Email</FormLabel>
                            <FormControl>
                                <Input
                                    type="email"
                                    placeholder="Enter your email"
                                    className="w-full bg-gray-100 border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Password</FormLabel>
                            <FormControl>
                                <Input
                                    type="password"
                                    placeholder="Enter your password"
                                    className="w-full bg-gray-100 border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <Button
                    type="submit"
                    className="w-full"
                    size="lg"
                    disabled={isLoading}
                    style={{ backgroundColor: '#5fb3b3' }}
                >
                    {isLoading ? 'Signing In...' : 'Sign In'}
                </Button>
            </form>
        </Form>
    );
}