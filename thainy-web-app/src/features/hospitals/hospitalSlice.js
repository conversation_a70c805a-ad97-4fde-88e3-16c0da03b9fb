import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axiosInstance from '../../services/axois';

const initialState = {
  hospitals: [],
  loading: false,
  error: null,
};

// Async thunk for fetching hospitals
export const fetchHospitalsThunk = createAsyncThunk(
  'hospitals/fetchHospitals',
  async ({ excludeSelf = false, exclude_hospital_name } = {}, { rejectWithValue }) => {
    try {
      const res = await axiosInstance.get('/hospitals', {
        params: {
          excludeSelf,
          exclude_hospital_name,
        },
      });
      return res.data; // expecting { status: 1, data: hospitals }
    } catch (e) {
      return rejectWithValue(e.response?.data || { message: e.message });
    }
  }
);

const hospitalSlice = createSlice({
  name: 'hospitals',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchHospitalsThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHospitalsThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.hospitals = action.payload.data || action.payload;
        state.error = null;
      })
      .addCase(fetchHospitalsThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to fetch hospitals';
      });
  },
});

export const { clearError } = hospitalSlice.actions;
export default hospitalSlice.reducer;
