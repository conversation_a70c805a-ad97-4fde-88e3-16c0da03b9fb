# Authentication System Documentation

## Overview

This authentication system uses Redux Toolkit with async thunks to manage user authentication state. It includes automatic localStorage persistence and route protection.

## Features

- ✅ Redux-based state management
- ✅ Async thunk for login/logout operations
- ✅ Automatic localStorage persistence
- ✅ Route protection (Private/Public routes)
- ✅ Error handling and loading states
- ✅ Custom useAuth hook for easy usage
- ✅ Automatic token management

## Files Structure

```
src/
├── features/auth/
│   ├── authSlice.js          # Redux slice with async thunks
│   └── README.md             # This documentation
├── hooks/
│   └── useAuth.js            # Custom hook for auth operations
├── routes/
│   ├── AppRoutes.jsx         # Main routing component
│   ├── PrivateRoutes.jsx     # Protected route wrapper
│   └── PublicRoutes.jsx      # Public route wrapper
└── pages/Auth/
    └── Login.jsx             # Login form component
```

## Usage

### 1. Using the useAuth Hook

```jsx
import { useAuth } from '../hooks/useAuth';

function LoginComponent() {
  const { login, loading, error, isAuthenticated } = useAuth();

  const handleLogin = async (credentials) => {
    const result = await login(credentials);
    if (result.success) {
      // Login successful
    } else {
      // Handle error: result.error
    }
  };
}
```

### 2. Protecting Routes

```jsx
// Private route (requires authentication)
<Route path="/dashboard" element={
  <PrivateRoute>
    <Dashboard />
  </PrivateRoute>
} />

// Public route (only accessible when NOT authenticated)
<Route path="/login" element={
  <PublicRoute>
    <LoginPage />
  </PublicRoute>
} />
```

### 3. Accessing Auth State

```jsx
import { useSelector } from 'react-redux';

function Component() {
  const { isAuthenticated, user, loading, error } = useSelector(state => state.auth);
  
  return (
    <div>
      {isAuthenticated ? `Welcome ${user.name}` : 'Please login'}
    </div>
  );
}
```

## Auth State Structure

```javascript
{
  isAuthenticated: boolean,    // Whether user is logged in
  user: object | null,         // User data (id, email, name, role)
  token: string | null,        // Authentication token
  loading: boolean,            // Loading state for async operations
  error: string | null         // Error message if operation fails
}
```

## API Integration

Currently using mock data for demo purposes. To integrate with real API:

1. Update `authSlice.js` in the `loginUser` thunk:
```javascript
// Replace the mock code with:
const response = await axiosInstance.post('/auth/login', credentials);
const { token, user } = response.data;
```

2. Update your API endpoint to return:
```javascript
{
  token: "your-jwt-token",
  user: {
    id: 1,
    email: "<EMAIL>",
    name: "User Name",
    role: "user"
  }
}
```

## Testing the System

1. Start the development server: `npm run dev`
2. Navigate to any route - you'll be redirected to `/login`
3. Enter any email and password (demo mode accepts anything)
4. You'll be redirected to `/dashboard` after successful login
5. Click logout to return to login page

## localStorage Keys

The system uses these localStorage keys:
- `authToken` - Authentication token
- `userData` - Serialized user object

## Error Handling

Errors are automatically handled and stored in Redux state. They can be:
- Displayed in UI components
- Cleared using `clearAuthError()` from useAuth hook
- Automatically cleared on successful operations
