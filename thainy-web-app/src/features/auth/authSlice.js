import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
// import axiosInstance from '../../services/axois'; // Uncomment when using real API

// Check if user is authenticated from localStorage
const checkAuthFromStorage = () => {
  const token = localStorage.getItem('authToken') || localStorage.getItem('token');
  const userData = localStorage.getItem('userData');

  return {
    isAuthenticated: !!token,
    token: token || null,
    user: userData ? JSON.parse(userData) : null
  };
};

// Initial state with data from localStorage
const initialState = {
  isAuthenticated: checkAuthFromStorage().isAuthenticated,
  user: checkAuthFromStorage().user,
  token: checkAuthFromStorage().token,
  loading: false,
  error: null,
};

// Async thunk for login
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials, { rejectWithValue }) => {
    try {
      // For demo purposes, simulate API call with mock data
      // Replace this with actual API call when backend is ready

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock validation - accept any email/password for demo
      if (!credentials.email || !credentials.password) {
        throw new Error('Email and password are required');
      }

      // Mock successful response
      const mockResponse = {
        data: {
          token: `demo-token-${Date.now()}`,
          user: {
            id: 1,
            email: credentials.email,
            name: credentials.email.split('@')[0], // Use email prefix as name
            role: 'user'
          }
        }
      };

      // Uncomment this when you have a real API endpoint:
      // const response = await axiosInstance.post('/auth/login', credentials);

      const { token, user } = mockResponse.data;

      // Store in localStorage
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(user));

      return { token, user };
    } catch (error) {
      // Handle different error scenarios
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Login failed';

      return rejectWithValue(errorMessage);
    }
  }
);

// Async thunk for logout
export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      // Optional: Make API call to logout endpoint
      // await axiosInstance.post('/auth/logout');

      // Clear localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('token');
      localStorage.removeItem('userData');

      return true;
    } catch (error) {
      // Even if API call fails, clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('token');
      localStorage.removeItem('userData');

      return rejectWithValue('Logout failed');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Synchronous actions
    clearError: (state) => {
      state.error = null;
    },
    // Check auth status from localStorage (useful for app initialization)
    checkAuthStatus: (state) => {
      const authData = checkAuthFromStorage();
      state.isAuthenticated = authData.isAuthenticated;
      state.user = authData.user;
      state.token = authData.token;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login cases
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = action.payload;
      })
      // Logout cases
      .addCase(logoutUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.loading = false;
        // Still clear auth state even if logout API fails
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = action.payload;
      });
  },
});

export const { clearError, checkAuthStatus } = authSlice.actions;
export default authSlice.reducer;
